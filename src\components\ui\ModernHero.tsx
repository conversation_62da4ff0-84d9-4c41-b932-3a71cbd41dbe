'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
// Removed motion import to avoid cache issues

interface ModernHeroProps {
  title: string;
  subtitle: string;
  backgroundImage?: string;
  backgroundVideo?: string;
  primaryCTA: {
    text: string;
    href: string;
  };
  secondaryCTA?: {
    text: string;
    href: string;
  };
  className?: string;
}

// Premium Animation variants inspired by Context7 patterns
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 1.2,
      staggerChildren: 0.15,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const titleVariants = {
  hidden: { opacity: 0, y: 60, scale: 0.95 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 1.0,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const subtitleVariants = {
  hidden: { opacity: 0, y: 40 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      delay: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const buttonVariants = {
  initial: { scale: 1, y: 0 },
  hover: {
    scale: 1.05,
    y: -2,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  },
  tap: { scale: 0.98 }
};

function ModernHeroComponent({
  title,
  subtitle,
  backgroundImage,
  backgroundVideo,
  primaryCTA,
  secondaryCTA,
  className = ''
}: ModernHeroProps) {
  // SSR-safe mounting detection
  const [mounted, setMounted] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  // Calculate transform values based on scroll
  const y = Math.min((scrollY / 500) * 150, 150);
  const opacity = Math.max(1 - (scrollY / 300), 0);

  useEffect(() => {
    setMounted(true);
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    // Add scroll listener for parallax effect
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section className={`relative min-h-screen flex items-center justify-center overflow-hidden ${className}`}>
      {/* Premium Background with Parallax */}
      <motion.div
        className="absolute inset-0 z-0"
        style={mounted ? { transform: `translateY(${y}px)` } : {}}
      >
        {backgroundVideo ? (
          <video
            autoPlay
            muted
            loop
            playsInline
            className="w-full h-full object-cover scale-110"
          >
            <source src={backgroundVideo} type="video/mp4" />
          </video>
        ) : backgroundImage ? (
          <div
            className="w-full h-full bg-cover bg-center bg-no-repeat scale-110"
            style={{
              backgroundImage: `url(${backgroundImage})`,
            }}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-slate-900 via-emerald-900 to-emerald-800 scale-110" />
        )}

        {/* Premium Overlay with Gradient Mesh */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/60" />

        {/* Animated Gradient Mesh - Only render after mounting */}
        {mounted && (
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-transparent to-emerald-600/10 animate-pulse" />
        )}

        {/* Noise Texture for Premium Feel - Only render after mounting */}
        {mounted && (
          <div className="absolute inset-0 opacity-20" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`
          }} />
        )}
      </motion.div>

      {/* Premium Content */}
      <motion.div
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
        style={mounted ? { opacity: opacity } : {}}
      >
        {mounted ? (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Premium Title with Gradient Text */}
            <motion.h1
              className="text-5xl sm:text-6xl lg:text-8xl font-bold mb-8 leading-[0.9] tracking-tight"
              variants={titleVariants}
            >
              <span className="bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                {title.split(' ').slice(0, -2).join(' ')}
              </span>
              <br />
              <span className="bg-gradient-to-r from-emerald-400 via-teal-300 to-blue-400 bg-clip-text text-transparent">
                {title.split(' ').slice(-2).join(' ')}
              </span>
            </motion.h1>

            {/* Premium Subtitle */}
            <motion.p
              className="text-xl sm:text-2xl lg:text-3xl text-gray-300 mb-16 max-w-5xl mx-auto leading-relaxed font-light"
              variants={subtitleVariants}
            >
              {subtitle}
            </motion.p>

            {/* Trust Indicators */}
            <motion.div
              className="flex flex-wrap justify-center items-center gap-8 mb-12 opacity-80"
              variants={subtitleVariants}
            >
              <div className="flex items-center text-gray-300">
                <svg className="w-5 h-5 text-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Local Expertise</span>
              </div>
              <div className="flex items-center text-gray-300">
                <svg className="w-5 h-5 text-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Premium Properties</span>
              </div>
              <div className="flex items-center text-gray-300">
                <svg className="w-5 h-5 text-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Full Service</span>
              </div>
            </motion.div>

            {/* Premium CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
              variants={subtitleVariants}
            >
              {/* Primary CTA - Premium Design */}
              <motion.div
                variants={buttonVariants}
                initial="initial"
                whileHover="hover"
                whileTap="tap"
              >
                <Link
                  href={primaryCTA.href}
                  className="group relative inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white bg-gradient-to-r from-emerald-600 via-emerald-500 to-emerald-700 rounded-2xl shadow-2xl transition-all duration-500 hover:shadow-emerald-500/40 hover:shadow-2xl overflow-hidden"
                >
                  {/* Animated Background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-500 via-emerald-600 to-emerald-700 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer" />

                  <span className="relative z-10 flex items-center">
                    {primaryCTA.text}
                    <motion.svg
                      className="w-5 h-5 ml-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </motion.svg>
                  </span>
                </Link>
              </motion.div>

              {/* Secondary CTA - Glass Morphism */}
              {secondaryCTA && (
                <motion.div
                  variants={buttonVariants}
                  initial="initial"
                  whileHover="hover"
                  whileTap="tap"
                >
                  <Link
                    href={secondaryCTA.href}
                    className="group inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white border border-white/20 rounded-2xl backdrop-blur-md bg-white/5 transition-all duration-500 hover:bg-white/10 hover:border-white/30 hover:backdrop-blur-lg"
                  >
                    <span className="flex items-center">
                      {secondaryCTA.text}
                      <motion.svg
                        className="w-5 h-5 ml-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        whileHover={{ x: 4 }}
                        transition={{ duration: 0.3 }}
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </motion.svg>
                    </span>
                  </Link>
                </motion.div>
              )}
            </motion.div>


          </motion.div>
        ) : (
          // Enhanced SSR Fallback
          <div>
            <h1 className="text-5xl sm:text-6xl lg:text-8xl font-bold mb-8 leading-[0.9] tracking-tight">
              <span className="bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                {title}
              </span>
            </h1>
            <p className="text-xl sm:text-2xl lg:text-3xl text-gray-300 mb-16 max-w-5xl mx-auto leading-relaxed font-light">
              {subtitle}
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link
                href={primaryCTA.href}
                className="inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl shadow-2xl"
              >
                {primaryCTA.text}
              </Link>
              {secondaryCTA && (
                <Link
                  href={secondaryCTA.href}
                  className="inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white border border-white/20 rounded-2xl backdrop-blur-md bg-white/5"
                >
                  {secondaryCTA.text}
                </Link>
              )}
            </div>
          </div>
        )}
      </motion.div>

      {/* Floating Elements for Premium Feel - Only render after mounting */}
      {mounted && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            className="absolute top-1/4 left-1/4 w-2 h-2 bg-emerald-400/30 rounded-full"
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute top-1/3 right-1/3 w-1 h-1 bg-emerald-400/40 rounded-full"
            animate={{
              y: [0, -15, 0],
              opacity: [0.4, 0.9, 0.4]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
          <motion.div
            className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-emerald-400/35 rounded-full"
            animate={{
              y: [0, -25, 0],
              opacity: [0.35, 0.7, 0.35]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
        </div>
      )}
    </section>
  );
}

export const ModernHero = ModernHeroComponent;
